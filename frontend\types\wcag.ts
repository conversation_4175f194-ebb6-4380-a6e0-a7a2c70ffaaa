/**
 * Frontend WCAG Type Definitions
 * Client-side types for WCAG functionality
 */

// Define frontend types that match backend types
export type WcagVersion = '2.1' | '2.2' | '3.0';
export type WcagLevel = 'A' | 'AA' | 'AAA';
export type WcagCategory = 'perceivable' | 'operable' | 'understandable' | 'robust';
export type ScanStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export type WcagRiskLevel = 'low' | 'medium' | 'high' | 'critical';
export type RiskLevel = WcagRiskLevel;
export type WcagAutomationLevel = 'automated' | 'semi-automated' | 'manual';
export type WcagCheckResult = 'pass' | 'fail' | 'inapplicable' | 'cantTell';
export type WcagCheckType = 'automated' | 'manual';

export interface WcagCategoryScores {
  perceivable: number;
  operable: number;
  understandable: number;
  robust: number;
}

export interface WcagVersionScores {
  wcag21: number;
  wcag22: number;
  wcag30: number;
}

export interface WcagScanSummary {
  totalAutomatedChecks: number;
  passedAutomatedChecks: number;
  failedAutomatedChecks: number;
  automatedScore: number;
  automationRate: number;
  manualReviewItems: number;
  categoryScores: WcagCategoryScores;
  versionScores: WcagVersionScores;
}

export interface WcagCheckEvidence {
  selector?: string;
  html?: string;
  screenshot?: string;
  description: string;
}

export interface WcagCheck {
  checkId: string;
  ruleId: string;
  title: string;
  description: string;
  category: WcagCategory;
  level: WcagLevel;
  wcagVersion: WcagVersion;
  result: WcagCheckResult;
  automationLevel: WcagAutomationLevel;
  evidence?: WcagCheckEvidence[];
  impact?: 'minor' | 'moderate' | 'serious' | 'critical';
  tags?: string[];
}

export interface WcagRecommendation {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: WcagCategory;
  estimatedEffort: string;
  resources?: string[];
}

export interface WcagScanMetadata {
  scanId: string;
  userId: string;
  requestId: string;
  startTime: Date;
  duration: number;
  userAgent: string;
  viewport: { width: number; height: number };
  environment: string;
  version: string;
}

export interface WcagScanOptions {
  enableContrastAnalysis: boolean;
  enableKeyboardTesting: boolean;
  enableFocusAnalysis: boolean;
  enableSemanticValidation: boolean;
  enableManualReview?: boolean;
  wcagVersion: WcagVersion | 'all';
  level: WcagLevel;
  maxPages: number;
  timeout?: number;
  // Aliases used in the frontend
  contrast?: boolean; // Alias for enableContrastAnalysis
  keyboard?: boolean; // Alias for enableKeyboardTesting
  focus?: boolean; // Alias for enableFocusAnalysis
  semantic?: boolean; // Alias for enableSemanticValidation
  pageLimit?: number; // Alias for maxPages
}

export interface WcagScanConfig {
  targetUrl: string;
  scanOptions: WcagScanOptions;
  userId: string;
  requestId: string;
}

export interface WcagScanResult {
  scanId: string;
  targetUrl: string;
  status: ScanStatus;
  overallScore: number;
  levelAchieved: WcagLevel;
  riskLevel: WcagRiskLevel;
  summary: WcagScanSummary;
  metadata: WcagScanMetadata;
  checks: WcagCheck[];
  recommendations: WcagRecommendation[];
  // Additional properties used in the frontend
  url: string; // Alias for targetUrl for backward compatibility
  scanTimestamp: string; // ISO string of when the scan was initiated
  completionTimestamp?: string; // When the scan was completed
  wcagVersion: WcagVersion | 'all'; // WCAG version used for the scan
  complianceLevel: WcagLevel; // Compliance level (A, AA, AAA)
  scanOptions: WcagScanOptions; // Options used for the scan
  manualReviewData?: Array<{
    id: string;
    ruleId: string;
    selector: string;
    description: string;
    automatedFindings: string;
    reviewRequired: string;
    priority: 'high' | 'medium' | 'low';
    estimatedTime: number;
    reviewStatus: 'pending' | 'in_progress' | 'completed' | 'skipped';
    reviewerNotes?: string;
    reviewAssessment?: string;
    reviewedAt?: Date;
    reviewedBy?: string;
  }>;
}

// Frontend-specific types
export interface WcagScanFormData {
  targetUrl: string;
  enableContrastAnalysis: boolean;
  enableKeyboardTesting: boolean;
  enableFocusAnalysis: boolean;
  enableSemanticValidation: boolean;
  enableManualReview?: boolean;
  wcagVersion: WcagVersion | 'all';
  level: WcagLevel;
  maxPages: number;

  // Enhanced Phase 1-3 Options
  enableBrowserPool?: boolean;
  enableSmartCache?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableCMSDetection?: boolean;
  enableEcommerceAnalysis?: boolean;
  enableFrameworkDetection?: boolean;
  enableMediaAnalysis?: boolean;
  enableVPSOptimization?: boolean;
  timeout?: number;

  // Advanced Options (from orphaned component)
  includeHiddenElements?: boolean;
  enableScreenshots?: boolean;
  customRules?: string[];
  excludeRules?: string[];

  // Authentication Options (from orphaned component)
  requiresAuth?: boolean;
  authType?: 'none' | 'basic' | 'form' | 'oauth';
  authCredentials?: {
    username: string;
    password: string;
  };
  customHeaders?: { [key: string]: string };
}

export interface WcagDashboardState {
  currentScan?: WcagScanResult;
  recentScans: WcagScanResult[];
  isScanning: boolean;
  scanProgress: number;
  error?: string;
  selectedScanId?: string;
}

export interface WcagComponentProps {
  scanResult?: WcagScanResult;
  onScanStart?: (config: WcagScanConfig) => void;
  onExport?: (scanId: string, format: 'pdf' | 'json' | 'csv') => void;
  onRescan?: (scanId: string) => void;
  onDelete?: (scanId: string) => void;
}

export interface ScanProgressInfo {
  scanId: string;
  status: ScanStatus;
  currentCheck?: string;
  completedChecks: number;
  totalChecks: number;
  progress: number;
  estimatedTimeRemaining?: number;
  checksCompleted?: number;
}

export interface QueueStatusInfo {
  totalQueued: number;
  currentlyProcessing: number;
  maxConcurrent: number;
  averageWaitTime: number;
  estimatedWaitTime: number;
  position?: number;
  pending?: number;
  running?: number;
  completed?: number;
}

// UI State types
export interface WcagUIState {
  activeTab: 'overview' | 'details' | 'recommendations' | 'history';
  selectedCategory?: WcagCategory;
  selectedLevel?: WcagLevel;
  showOnlyFailed: boolean;
  sortBy: 'ruleId' | 'score' | 'category' | 'level';
  sortOrder: 'asc' | 'desc';
  pageSize: number;
  currentPage: number;
}

// Chart data types
export interface ScoreChartData {
  category: string;
  score: number;
  maxScore: number;
  color: string;
}

export interface TrendChartData {
  date: string;
  score: number;
  level: string;
}

export interface ComplianceBreakdownData {
  level: WcagLevel;
  passed: number;
  failed: number;
  total: number;
  percentage: number;
}
