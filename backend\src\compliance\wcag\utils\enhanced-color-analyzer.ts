/**
 * Enhanced Color Analyzer for WCAG Scanning
 * Advanced color contrast analysis with gradient, CSS custom properties, and complex background support
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface ColorInfo {
  r: number;
  g: number;
  b: number;
  a: number;
  hex: string;
  hsl: { h: number; s: number; l: number };
  luminance: number;
}

export interface GradientInfo {
  type: 'linear' | 'radial' | 'conic';
  direction?: string;
  stops: Array<{
    color: ColorInfo;
    position: number; // 0-1
  }>;
  averageColor: ColorInfo;
  contrastRange: { min: number; max: number };
}

export interface BackgroundAnalysis {
  type: 'solid' | 'gradient' | 'image' | 'pattern' | 'complex';
  primaryColor?: ColorInfo;
  gradient?: GradientInfo;
  imageUrl?: string;
  hasTransparency: boolean;
  effectiveColor: ColorInfo; // Best representative color for contrast calculation
  confidence: number; // 0-1, how confident we are in the analysis
}

export interface TextAnalysis {
  text: string;
  color: ColorInfo;
  fontSize: number;
  fontWeight: string | number;
  isLarge: boolean; // >= 18pt regular or >= 14pt bold
  hasTextShadow: boolean;
  hasOutline: boolean;
  effectiveColor: ColorInfo; // Color after considering shadows/outlines
}

export interface ContrastResult {
  ratio: number;
  passes: {
    aa: boolean;
    aaa: boolean;
    aaLarge: boolean;
    aaaLarge: boolean;
  };
  recommendation: string;
  confidence: number;
  issues: string[];
}

export interface EnhancedColorAnalysisResult {
  element: string; // selector or xpath
  text: TextAnalysis;
  background: BackgroundAnalysis;
  contrast: ContrastResult;
  cssCustomProperties: Record<string, string>;
  computedStyles: Record<string, string>;
  accessibility: {
    isAccessible: boolean;
    level: 'AA' | 'AAA' | 'fail';
    improvements: string[];
  };
}

/**
 * Advanced color contrast analyzer with comprehensive background detection
 */
export class EnhancedColorAnalyzer {
  private static instance: EnhancedColorAnalyzer;
  private cssCustomPropsCache = new Map<string, Record<string, string>>();

  private constructor() {}

  static getInstance(): EnhancedColorAnalyzer {
    if (!EnhancedColorAnalyzer.instance) {
      EnhancedColorAnalyzer.instance = new EnhancedColorAnalyzer();
    }
    return EnhancedColorAnalyzer.instance;
  }

  /**
   * Analyze color contrast for all text elements on the page
   */
  async analyzePageContrast(page: Page): Promise<EnhancedColorAnalysisResult[]> {
    logger.debug('🎨 Starting enhanced color contrast analysis');

    // Inject enhanced color analysis functions
    await this.injectColorAnalysisFunctions(page);

    // Get all text elements with their computed styles
    const textElements = await page.evaluate(() => {
      return (window as any).wcagEnhancedColorAnalysis.analyzeAllTextElements();
    });

    const results: EnhancedColorAnalysisResult[] = [];

    for (const element of textElements) {
      try {
        const analysis = await this.analyzeElementContrast(page, element);
        if (analysis) {
          results.push(analysis);
        }
      } catch (error) {
        logger.warn(`Failed to analyze element contrast: ${element.selector}`, {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    logger.info(`✅ Enhanced color analysis completed: ${results.length} elements analyzed`);
    return results;
  }

  /**
   * Analyze contrast for a specific element
   */
  async analyzeElementContrast(
    page: Page,
    elementData: any,
  ): Promise<EnhancedColorAnalysisResult | null> {
    try {
      // Get enhanced element analysis
      const analysis = await page.evaluate((selector) => {
        return (window as any).wcagEnhancedColorAnalysis.analyzeElement(selector);
      }, elementData.selector);

      if (!analysis) return null;

      // Process the analysis data
      const textAnalysis = this.processTextAnalysis(analysis.text);
      const backgroundAnalysis = this.processBackgroundAnalysis(analysis.background);
      const contrastResult = this.calculateEnhancedContrast(textAnalysis, backgroundAnalysis);

      return {
        element: elementData.selector,
        text: textAnalysis,
        background: backgroundAnalysis,
        contrast: contrastResult,
        cssCustomProperties: analysis.customProperties || {},
        computedStyles: analysis.computedStyles || {},
        accessibility: this.assessAccessibility(contrastResult, textAnalysis),
      };
    } catch (error) {
      logger.warn(`Error analyzing element contrast: ${elementData.selector}`, {
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * Inject enhanced color analysis functions into the page
   */
  private async injectColorAnalysisFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as any).wcagEnhancedColorAnalysis = {
        /**
         * Analyze all text elements on the page
         */
        analyzeAllTextElements() {
          const textElements: any[] = [];
          const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
              const text = node.textContent?.trim();
              if (!text || text.length < 2) return NodeFilter.FILTER_REJECT;

              const parent = node.parentElement;
              if (!parent) return NodeFilter.FILTER_REJECT;

              // Skip hidden elements
              const style = window.getComputedStyle(parent);
              if (
                style.display === 'none' ||
                style.visibility === 'hidden' ||
                style.opacity === '0'
              ) {
                return NodeFilter.FILTER_REJECT;
              }

              return NodeFilter.FILTER_ACCEPT;
            },
          });

          let node;
          while ((node = walker.nextNode())) {
            const element = node.parentElement!;
            const selector = this.getElementSelector(element);

            textElements.push({
              selector,
              text: node.textContent?.trim(),
              element: element.tagName.toLowerCase(),
            });
          }

          return textElements;
        },

        /**
         * Analyze a specific element
         */
        analyzeElement(selector: string) {
          const element = document.querySelector(selector) as HTMLElement;
          if (!element) return null;

          const computedStyle = window.getComputedStyle(element);

          return {
            text: this.analyzeTextProperties(element, computedStyle),
            background: this.analyzeBackgroundProperties(element, computedStyle),
            customProperties: this.extractCustomProperties(element),
            computedStyles: this.getRelevantComputedStyles(computedStyle),
          };
        },

        /**
         * Analyze text properties
         */
        analyzeTextProperties(element: HTMLElement, style: CSSStyleDeclaration) {
          const color = this.parseColor(style.color);
          const fontSize = parseFloat(style.fontSize);
          const fontWeight = style.fontWeight;

          // Determine if text is large (18pt+ regular or 14pt+ bold)
          const isLarge = fontSize >= 18 || (fontSize >= 14 && this.isBoldWeight(fontWeight));

          // Check for text effects
          const hasTextShadow = style.textShadow !== 'none';
          const hasOutline =
            style.webkitTextStroke !== 'none' || (style as any).textStroke !== 'none';

          // Calculate effective color (considering shadows/outlines)
          const effectiveColor = this.calculateEffectiveTextColor(color, style);

          return {
            text: element.textContent?.trim() || '',
            color,
            fontSize,
            fontWeight,
            isLarge,
            hasTextShadow,
            hasOutline,
            effectiveColor,
          };
        },

        /**
         * Analyze background properties with gradient and image support
         */
        analyzeBackgroundProperties(element: HTMLElement, style: CSSStyleDeclaration) {
          const background = style.background || style.backgroundColor;

          // Check for gradients
          if (background.includes('gradient')) {
            return this.analyzeGradientBackground(background, element);
          }

          // Check for images
          if (background.includes('url(')) {
            return this.analyzeImageBackground(background, style, element);
          }

          // Solid color background
          const backgroundColor = this.parseColor(style.backgroundColor);

          // Walk up the DOM tree to find effective background
          const effectiveBackground = this.findEffectiveBackground(element);

          return {
            type: 'solid',
            primaryColor: backgroundColor,
            hasTransparency: backgroundColor.a < 1,
            effectiveColor: effectiveBackground,
            confidence: backgroundColor.a === 0 ? 0.5 : 0.9,
          };
        },

        /**
         * Analyze gradient backgrounds
         */
        analyzeGradientBackground(background: string, element: HTMLElement) {
          const gradientMatch = background.match(/(linear|radial|conic)-gradient\([^)]+\)/);
          if (!gradientMatch) {
            return { type: 'complex', confidence: 0.3 };
          }

          const gradientString = gradientMatch[0];
          const type = gradientMatch[1] as 'linear' | 'radial' | 'conic';

          // Parse gradient stops
          const stops = this.parseGradientStops(gradientString);

          // Calculate average color
          const averageColor = this.calculateAverageGradientColor(stops);

          // Calculate contrast range
          const contrastRange = this.calculateGradientContrastRange(stops);

          return {
            type: 'gradient',
            gradient: {
              type,
              stops,
              averageColor,
              contrastRange,
            },
            hasTransparency: stops.some((stop: any) => stop.color.a < 1),
            effectiveColor: averageColor,
            confidence: 0.7,
          };
        },

        /**
         * Analyze image backgrounds
         */
        analyzeImageBackground(
          background: string,
          style: CSSStyleDeclaration,
          element: HTMLElement,
        ) {
          const urlMatch = background.match(/url\(['"]?([^'"]+)['"]?\)/);
          const imageUrl = urlMatch ? urlMatch[1] : '';

          // Try to get fallback background color
          const fallbackColor = this.parseColor(style.backgroundColor);

          // For images, we need to sample colors or use fallback
          return {
            type: 'image',
            imageUrl,
            primaryColor: fallbackColor,
            hasTransparency: true,
            effectiveColor: fallbackColor.a > 0 ? fallbackColor : { r: 128, g: 128, b: 128, a: 1 },
            confidence: fallbackColor.a > 0 ? 0.6 : 0.3,
          };
        },

        /**
         * Find effective background by walking up DOM tree
         */
        findEffectiveBackground(element: HTMLElement): any {
          let current = element;

          while (current && current !== document.body) {
            const style = window.getComputedStyle(current);
            const bgColor = this.parseColor(style.backgroundColor);

            if (bgColor.a > 0) {
              return bgColor;
            }

            current = current.parentElement!;
          }

          // Default to white background
          return { r: 255, g: 255, b: 255, a: 1, hex: '#ffffff' };
        },

        /**
         * Parse color string to color object
         */
        parseColor(colorString: string) {
          // Handle CSS custom properties
          if (colorString.startsWith('var(')) {
            colorString = this.resolveCustomProperty(colorString);
          }

          // Create a temporary element to parse color
          const temp = document.createElement('div');
          temp.style.color = colorString;
          document.body.appendChild(temp);

          const computed = window.getComputedStyle(temp).color;
          document.body.removeChild(temp);

          // Parse rgb/rgba
          const match = computed.match(/rgba?\(([^)]+)\)/);
          if (match) {
            const values = match[1].split(',').map((v) => parseFloat(v.trim()));
            const r = values[0] || 0;
            const g = values[1] || 0;
            const b = values[2] || 0;
            const a = values[3] !== undefined ? values[3] : 1;

            return {
              r,
              g,
              b,
              a,
              hex: this.rgbToHex(r, g, b),
              hsl: this.rgbToHsl(r, g, b),
              luminance: this.calculateLuminance(r, g, b),
            };
          }

          // Fallback to black
          return {
            r: 0,
            g: 0,
            b: 0,
            a: 1,
            hex: '#000000',
            hsl: { h: 0, s: 0, l: 0 },
            luminance: 0,
          };
        },

        /**
         * Resolve CSS custom property
         */
        resolveCustomProperty(varString: string): string {
          const match = varString.match(/var\(([^,)]+)(?:,\s*([^)]+))?\)/);
          if (!match) return varString;

          const propName = match[1].trim();
          const fallback = match[2]?.trim() || 'transparent';

          // Get the custom property value
          const value = getComputedStyle(document.documentElement).getPropertyValue(propName);
          return value.trim() || fallback;
        },

        /**
         * Extract CSS custom properties
         */
        extractCustomProperties(element: HTMLElement): Record<string, string> {
          const props: Record<string, string> = {};
          const style = window.getComputedStyle(element);

          // Get all CSS custom properties from the element and its ancestors
          let current: HTMLElement | null = element;
          while (current) {
            const currentStyle = window.getComputedStyle(current);

            // Extract custom properties (this is a simplified approach)
            for (let i = 0; i < currentStyle.length; i++) {
              const prop = currentStyle[i];
              if (prop.startsWith('--')) {
                props[prop] = currentStyle.getPropertyValue(prop);
              }
            }

            current = current.parentElement;
          }

          return props;
        },

        /**
         * Calculate luminance for contrast ratio
         */
        calculateLuminance(r: number, g: number, b: number): number {
          const [rs, gs, bs] = [r, g, b].map((c) => {
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
          });

          return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
        },

        /**
         * Helper functions
         */
        rgbToHex(r: number, g: number, b: number): string {
          return (
            '#' +
            [r, g, b]
              .map((x) => {
                const hex = Math.round(x).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
              })
              .join('')
          );
        },

        rgbToHsl(r: number, g: number, b: number): { h: number; s: number; l: number } {
          r /= 255;
          g /= 255;
          b /= 255;
          const max = Math.max(r, g, b),
            min = Math.min(r, g, b);
          let h = 0,
            s = 0,
            l = (max + min) / 2;

          if (max !== min) {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
              case r:
                h = (g - b) / d + (g < b ? 6 : 0);
                break;
              case g:
                h = (b - r) / d + 2;
                break;
              case b:
                h = (r - g) / d + 4;
                break;
            }
            h /= 6;
          }

          return { h: h * 360, s: s * 100, l: l * 100 };
        },

        isBoldWeight(weight: string | number): boolean {
          if (typeof weight === 'number') return weight >= 600;
          return ['bold', 'bolder', '600', '700', '800', '900'].includes(weight);
        },

        getElementSelector(element: HTMLElement): string {
          if (element.id) return `#${element.id}`;

          const path = [];
          while (element && element.nodeType === Node.ELEMENT_NODE) {
            let selector = element.nodeName.toLowerCase();
            if (element.className) {
              selector += '.' + element.className.split(' ').join('.');
            }
            path.unshift(selector);
            element = element.parentElement!;
            if (path.length > 5) break; // Limit depth
          }

          return path.join(' > ');
        },

        getRelevantComputedStyles(style: CSSStyleDeclaration): Record<string, string> {
          const relevant = [
            'color',
            'backgroundColor',
            'background',
            'fontSize',
            'fontWeight',
            'textShadow',
            'webkitTextStroke',
            'opacity',
            'filter',
          ];

          const styles: Record<string, string> = {};
          relevant.forEach((prop) => {
            styles[prop] = style.getPropertyValue(prop);
          });

          return styles;
        },
      };
    });
  }

  /**
   * Process text analysis data
   */
  private processTextAnalysis(textData: any): TextAnalysis {
    return {
      text: textData.text || '',
      color: textData.color || { r: 0, g: 0, b: 0, a: 1 },
      fontSize: textData.fontSize || 16,
      fontWeight: textData.fontWeight || 'normal',
      isLarge: textData.isLarge || false,
      hasTextShadow: textData.hasTextShadow || false,
      hasOutline: textData.hasOutline || false,
      effectiveColor: textData.effectiveColor || textData.color,
    };
  }

  /**
   * Process background analysis data
   */
  private processBackgroundAnalysis(backgroundData: any): BackgroundAnalysis {
    return {
      type: backgroundData.type || 'solid',
      primaryColor: backgroundData.primaryColor,
      gradient: backgroundData.gradient,
      imageUrl: backgroundData.imageUrl,
      hasTransparency: backgroundData.hasTransparency || false,
      effectiveColor: backgroundData.effectiveColor || { r: 255, g: 255, b: 255, a: 1 },
      confidence: backgroundData.confidence || 0.5,
    };
  }

  /**
   * Calculate enhanced contrast ratio
   */
  private calculateEnhancedContrast(
    text: TextAnalysis,
    background: BackgroundAnalysis,
  ): ContrastResult {
    const textLuminance =
      text.effectiveColor.luminance || this.calculateLuminance(text.effectiveColor);
    const bgLuminance =
      background.effectiveColor.luminance || this.calculateLuminance(background.effectiveColor);

    const ratio =
      (Math.max(textLuminance, bgLuminance) + 0.05) / (Math.min(textLuminance, bgLuminance) + 0.05);

    // WCAG contrast requirements
    const passes = {
      aa: text.isLarge ? ratio >= 3 : ratio >= 4.5,
      aaa: text.isLarge ? ratio >= 4.5 : ratio >= 7,
      aaLarge: ratio >= 3,
      aaaLarge: ratio >= 4.5,
    };

    const issues: string[] = [];
    let recommendation = '';

    if (!passes.aa) {
      issues.push('Does not meet WCAG AA contrast requirements');
      recommendation = `Increase contrast ratio to at least ${text.isLarge ? '3:1' : '4.5:1'} for AA compliance`;
    } else if (!passes.aaa) {
      recommendation = `Consider increasing contrast ratio to ${text.isLarge ? '4.5:1' : '7:1'} for AAA compliance`;
    } else {
      recommendation = 'Excellent contrast ratio - meets all WCAG requirements';
    }

    // Adjust confidence based on background complexity
    let confidence = background.confidence;
    if (background.type === 'gradient') {
      confidence *= 0.8;
      issues.push('Gradient background detected - manual verification recommended');
    } else if (background.type === 'image') {
      confidence *= 0.6;
      issues.push('Image background detected - manual verification required');
    }

    return {
      ratio: Math.round(ratio * 100) / 100,
      passes,
      recommendation,
      confidence,
      issues,
    };
  }

  /**
   * Calculate luminance for a color
   */
  private calculateLuminance(color: ColorInfo): number {
    const { r, g, b } = color;
    const [rs, gs, bs] = [r, g, b].map((c) => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  /**
   * Assess accessibility level
   */
  private assessAccessibility(
    contrast: ContrastResult,
    text: TextAnalysis,
  ): {
    isAccessible: boolean;
    level: 'AA' | 'AAA' | 'fail';
    improvements: string[];
  } {
    const improvements: string[] = [];

    if (!contrast.passes.aa) {
      improvements.push('Increase color contrast to meet WCAG AA standards');
      improvements.push('Consider using darker text or lighter background colors');

      return {
        isAccessible: false,
        level: 'fail',
        improvements,
      };
    }

    if (!contrast.passes.aaa) {
      improvements.push('Consider increasing contrast for AAA compliance');
      improvements.push('Test with users who have visual impairments');
    }

    if (text.hasTextShadow || text.hasOutline) {
      improvements.push('Verify that text effects do not interfere with readability');
    }

    return {
      isAccessible: true,
      level: contrast.passes.aaa ? 'AAA' : 'AA',
      improvements,
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cssCustomPropsCache.clear();
  }
}

export default EnhancedColorAnalyzer;
